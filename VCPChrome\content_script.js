let lastPageContent = '';
let vcpIdCounter = 0;

// 增量更新系统
class IncrementalUpdateManager {
    constructor() {
        this.lastPageHash = '';
        this.elementCache = new Map(); // 缓存元素信息
        this.interactiveElements = new Map(); // 缓存可交互元素
        this.lastUpdateTime = 0;
        this.updateThrottle = 500; // 更新节流时间
        this.maxCacheSize = 1000; // 最大缓存大小
        this.performanceMetrics = {
            parseTime: 0,
            cacheHits: 0,
            cacheMisses: 0,
            totalUpdates: 0
        };
    }

    // 计算页面内容哈希
    calculatePageHash() {
        const content = document.documentElement.outerHTML;
        return this.simpleHash(content);
    }

    // 简单哈希函数
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString();
    }

    // 检查是否需要更新
    shouldUpdate() {
        const now = Date.now();
        if (now - this.lastUpdateTime < this.updateThrottle) {
            return false;
        }

        const currentHash = this.calculatePageHash();
        if (currentHash === this.lastPageHash) {
            return false;
        }

        this.lastPageHash = currentHash;
        this.lastUpdateTime = now;
        return true;
    }

    // 清理缓存
    cleanCache() {
        if (this.elementCache.size > this.maxCacheSize) {
            // 删除最旧的一半缓存
            const entries = Array.from(this.elementCache.entries());
            const toDelete = entries.slice(0, Math.floor(entries.length / 2));
            toDelete.forEach(([key]) => this.elementCache.delete(key));
        }
    }

    // 获取性能指标
    getMetrics() {
        return { ...this.performanceMetrics };
    }

    // 重置指标
    resetMetrics() {
        this.performanceMetrics = {
            parseTime: 0,
            cacheHits: 0,
            cacheMisses: 0,
            totalUpdates: 0
        };
    }
}

const updateManager = new IncrementalUpdateManager();

// 安全控制管理器
class SecurityManager {
    constructor() {
        this.settings = {
            enableWhitelist: false,
            whitelist: [],
            blacklist: [],
            blockSensitive: false,
            updateInterval: 500,
            enableIncrementalUpdate: true
        };
        this.currentDomain = window.location.hostname;
        this.loadSettings();
    }

    // 加载安全设置
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get([
                'enableWhitelist', 'whitelist', 'blacklist',
                'blockSensitive', 'updateInterval', 'enableIncrementalUpdate'
            ]);

            this.settings = {
                enableWhitelist: result.enableWhitelist || false,
                whitelist: this.parseHostList(result.whitelist || ''),
                blacklist: this.parseHostList(result.blacklist || ''),
                blockSensitive: result.blockSensitive || false,
                updateInterval: result.updateInterval || 500,
                enableIncrementalUpdate: result.enableIncrementalUpdate !== false
            };

            console.log('Security settings loaded:', this.settings);
        } catch (error) {
            console.error('Failed to load security settings:', error);
        }
    }

    // 解析主机列表
    parseHostList(hostString) {
        return hostString.split('\n')
            .map(host => host.trim())
            .filter(host => host.length > 0);
    }

    // 检查当前网站是否被允许
    isCurrentSiteAllowed() {
        // 检查黑名单
        if (this.isInBlacklist(this.currentDomain)) {
            console.log('Current site is blacklisted:', this.currentDomain);
            return false;
        }

        // 如果启用了白名单，检查白名单
        if (this.settings.enableWhitelist) {
            const allowed = this.isInWhitelist(this.currentDomain);
            if (!allowed) {
                console.log('Current site not in whitelist:', this.currentDomain);
            }
            return allowed;
        }

        return true;
    }

    // 检查是否在白名单中
    isInWhitelist(domain) {
        return this.settings.whitelist.some(whitelistDomain =>
            domain.includes(whitelistDomain) || whitelistDomain.includes(domain)
        );
    }

    // 检查是否在黑名单中
    isInBlacklist(domain) {
        return this.settings.blacklist.some(blacklistDomain =>
            domain.includes(blacklistDomain) || blacklistDomain.includes(domain)
        );
    }

    // 过滤敏感信息
    filterSensitiveInfo(content) {
        if (!this.settings.blockSensitive) {
            return content;
        }

        // 敏感信息模式
        const sensitivePatterns = [
            /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // 信用卡号
            /\b\d{3}-\d{2}-\d{4}\b/g, // 社会安全号
            /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // 邮箱（部分遮蔽）
            /\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b/g, // 电话号码
            /\b\d{6,}\b/g // 长数字串（可能是账号）
        ];

        let filteredContent = content;
        sensitivePatterns.forEach(pattern => {
            filteredContent = filteredContent.replace(pattern, '[敏感信息已隐藏]');
        });

        return filteredContent;
    }

    // 检查命令是否被允许
    isCommandAllowed(command, element) {
        // 在敏感网站上限制某些操作
        if (this.isInBlacklist(this.currentDomain)) {
            const restrictedCommands = ['type', 'upload', 'keypress'];
            if (restrictedCommands.includes(command)) {
                return false;
            }
        }

        // 检查元素类型
        if (element && this.settings.blockSensitive) {
            const sensitiveTypes = ['password', 'email', 'tel', 'number'];
            if (element.type && sensitiveTypes.includes(element.type)) {
                return false;
            }
        }

        return true;
    }

    // 更新设置
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        if (newSettings.whitelist) {
            this.settings.whitelist = this.parseHostList(newSettings.whitelist);
        }
        if (newSettings.blacklist) {
            this.settings.blacklist = this.parseHostList(newSettings.blacklist);
        }
    }
}

const securityManager = new SecurityManager();

function isInteractive(node) {
    if (node.nodeType !== Node.ELEMENT_NODE) {
        return false;
    }
    // 如果元素不可见，则它不是可交互的
    const style = window.getComputedStyle(node);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0' || style.height === '0' || style.width === '0') {
        return false;
    }

    const tagName = node.tagName.toLowerCase();
    const role = node.getAttribute('role');

    // 1. 标准的可交互元素
    if (['a', 'button', 'input', 'textarea', 'select', 'option'].includes(tagName)) {
        return true;
    }

    // 2. 常见的可交互ARIA角色
    if (role && ['button', 'link', 'checkbox', 'radio', 'menuitem', 'tab', 'switch', 'option', 'treeitem', 'searchbox', 'textbox', 'combobox'].includes(role)) {
        return true;
    }

    // 3. 通过JS属性明确可点击
    if (node.hasAttribute('onclick')) {
        return true;
    }

    // 4. 可聚焦的元素（非禁用）
    if (node.hasAttribute('tabindex') && node.getAttribute('tabindex') !== '-1') {
        return true;
    }
    
    // 5. 样式上被设计为可交互的元素
    if (style.cursor === 'pointer') {
        // 避免标记body或仅用于包裹的巨大容器
        if (tagName === 'body' || tagName === 'html') return false;
        // 如果一个元素没有文本内容但有子元素，它可能只是一个包装器
        if ((node.innerText || '').trim().length === 0 && node.children.length > 0) {
             // 但如果这个包装器有role属性，它可能是一个自定义组件
             if (!role) return false;
        }
        return true;
    }

    return false;
}


function pageToMarkdown(forceUpdate = false) {
    try {
        const startTime = performance.now();

        // 检查是否需要更新（除非强制更新）
        if (!forceUpdate && !updateManager.shouldUpdate()) {
            return lastPageContent;
        }

        updateManager.performanceMetrics.totalUpdates++;

        // 为确保每次都是全新的抓取，先移除所有旧的vcp-id
        document.querySelectorAll('[vcp-id]').forEach(el => el.removeAttribute('vcp-id'));
        vcpIdCounter = 0; // 重置计数器
        const body = document.body;
        if (!body) {
            return '';
        }

        let markdown = `# ${document.title}\nURL: ${document.URL}\n\n`;
        const ignoredTags = ['SCRIPT', 'STYLE', 'NAV', 'FOOTER', 'ASIDE', 'IFRAME', 'NOSCRIPT'];
        const processedNodes = new WeakSet(); // 记录已处理过的节点，防止重复

        function processNode(node) {
            // 1. 基本过滤条件
            if (!node || processedNodes.has(node)) return '';

            if (node.nodeType === Node.ELEMENT_NODE) {
                const style = window.getComputedStyle(node);
                if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
                    return '';
                }
                if (ignoredTags.includes(node.tagName)) {
                    return '';
                }
            }

            // 如果父元素已经被标记为可交互元素并处理过，则跳过此节点
            if (node.parentElement && node.parentElement.closest('[vcp-id]')) {
                return '';
            }

            // 2. 优先处理可交互元素
            if (isInteractive(node)) {
                const interactiveMd = formatInteractiveElement(node);
                if (interactiveMd) {
                    // 标记此节点及其所有子孙节点为已处理
                    processedNodes.add(node);
                    node.querySelectorAll('*').forEach(child => processedNodes.add(child));
                    return interactiveMd + '\n';
                }
            }

            // 3. 处理文本节点
            if (node.nodeType === Node.TEXT_NODE) {
                // 用正则表达式替换多个空白为一个空格
                return node.textContent.replace(/\s+/g, ' ').trim() + ' ';
            }

            // 4. 递归处理子节点 (包括 Shadow DOM)
            let childContent = '';
            if (node.shadowRoot) {
                childContent += processNode(node.shadowRoot);
            }
            
            node.childNodes.forEach(child => {
                childContent += processNode(child);
            });

            // 5. 为块级元素添加换行以保持结构
            if (node.nodeType === Node.ELEMENT_NODE && childContent.trim()) {
                const style = window.getComputedStyle(node);
                if (style.display === 'block' || style.display === 'flex' || style.display === 'grid') {
                    return '\n' + childContent.trim() + '\n';
                }
            }

            return childContent;
        }

        markdown += processNode(body);

        // 清理最终的Markdown文本
        markdown = markdown.replace(/[ \t]+/g, ' '); // 合并多余空格
        markdown = markdown.replace(/ (\n)/g, '\n'); // 清理行尾空格
        markdown = markdown.replace(/(\n\s*){3,}/g, '\n\n'); // 合并多余空行
        markdown = markdown.trim();

        // 记录性能指标
        const endTime = performance.now();
        updateManager.performanceMetrics.parseTime = endTime - startTime;

        // 清理缓存
        updateManager.cleanCache();

        return markdown;
    } catch (e) {
        console.error('Error in pageToMarkdown:', e);
        return `# ${document.title}\n\n[处理页面时出错: ${e.message}]`;
    }
}


function formatInteractiveElement(el) {
    // 避免重复标记同一个元素
    if (el.hasAttribute('vcp-id')) {
        return '';
    }

    vcpIdCounter++;
    const vcpId = `vcp-id-${vcpIdCounter}`;
    el.setAttribute('vcp-id', vcpId);

    let text = (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().replace(/\s+/g, ' ');
    const tagName = el.tagName.toLowerCase();
    const role = el.getAttribute('role');

    if (role === 'combobox' || role === 'searchbox') {
        const label = findLabelForInput(el);
        return `[输入框: ${label || text || el.name || el.id || '无标题输入框'}](${vcpId})`;
    }

    if (tagName === 'a' && el.href) {
        return `[链接: ${text || '无标题链接'}](${vcpId})`;
    }

    if (tagName === 'button' || role === 'button' || (tagName === 'input' && ['button', 'submit', 'reset'].includes(el.type))) {
        return `[按钮: ${text || '无标题按钮'}](${vcpId})`;
    }

    if (tagName === 'input' && !['button', 'submit', 'reset', 'hidden'].includes(el.type)) {
        const label = findLabelForInput(el);
        return `[输入框: ${label || text || el.name || el.id || '无标题输入框'}](${vcpId})`;
    }

    if (tagName === 'textarea') {
        const label = findLabelForInput(el);
        return `[文本区域: ${label || text || el.name || el.id || '无标题文本区域'}](${vcpId})`;
    }

    if (tagName === 'select') {
        const label = findLabelForInput(el);
        return `[下拉选择: ${label || text || el.name || el.id || '无标题下拉框'}](${vcpId})`;
    }

    // 为其他所有可交互元素（如可点击的div，带角色的span等）提供通用处理
    if (text) {
        return `[可交互元素: ${text}](${vcpId})`;
    }

    // 如果元素没有文本但仍然是可交互的（例如，一个图标按钮），我们仍然需要标记它
    // 但我们不回退ID，而是将其标记为一个没有文本的元素
    const type = el.type || role || tagName;
    return `[可交互元素: 无文本 (${type})](${vcpId})`;
}

function findLabelForInput(inputElement) {
    if (inputElement.id) {
        const label = document.querySelector(`label[for="${inputElement.id}"]`);
        if (label) return label.innerText.trim();
    }
    const parentLabel = inputElement.closest('label');
    if (parentLabel) return parentLabel.innerText.trim();
    return null;
}

function sendPageInfoUpdate() {
    // 检查安全权限
    if (!securityManager.isCurrentSiteAllowed()) {
        console.log('Page info update blocked by security policy');
        return;
    }

    const currentPageContent = pageToMarkdown();
    if (currentPageContent && currentPageContent !== lastPageContent) {
        // 过滤敏感信息
        const filteredContent = securityManager.filterSensitiveInfo(currentPageContent);
        lastPageContent = filteredContent;

        chrome.runtime.sendMessage({
            type: 'PAGE_INFO_UPDATE',
            data: {
                markdown: filteredContent,
                domain: window.location.hostname,
                securityLevel: securityManager.isInBlacklist(window.location.hostname) ? 'restricted' : 'normal'
            }
        }, () => {
            // 检查 chrome.runtime.lastError 以优雅地处理上下文失效的错误
            if (chrome.runtime.lastError) {
                // console.log("Page info update failed, context likely invalidated.");
            }
        });
    }
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'CLEAR_STATE') {
        lastPageContent = '';
    } else if (request.type === 'REQUEST_PAGE_INFO_UPDATE') {
        sendPageInfoUpdate();
    } else if (request.type === 'EXECUTE_COMMAND') {
        const { command, target, text, requestId, sourceClientId } = request.data;
        let result = {};

        try {
            console.log('Executing command:', { command, target, text, requestId });

            // 检查安全权限
            if (!securityManager.isCurrentSiteAllowed()) {
                throw new Error('当前网站被安全策略阻止执行操作');
            }

            // 处理不需要目标元素的命令
            if (['scroll', 'capture_page', 'debug_page', 'get_links', 'find_search_boxes'].includes(command)) {
                result = await executeAdvancedCommand(command, null, text, request.data);
            } else {
                // 需要目标元素的命令
                if (!target || target === 'undefined') {
                    throw new Error(`命令 '${command}' 需要指定目标元素，但收到的目标为: '${target}'`);
                }

                let element = document.querySelector(`[vcp-id="${target}"]`);

                if (!element) {
                    const allInteractiveElements = document.querySelectorAll('[vcp-id]');
                    for (const el of allInteractiveElements) {
                        const elText = (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().replace(/\s+/g, ' ');
                        if (elText === target) {
                            element = el;
                            break;
                        }
                    }
                }

                if (!element) {
                    // 提供更详细的调试信息
                    const availableElements = Array.from(document.querySelectorAll('[vcp-id]')).map(el => ({
                        id: el.getAttribute('vcp-id'),
                        text: (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().replace(/\s+/g, ' ').substring(0, 50),
                        tag: el.tagName.toLowerCase()
                    }));

                    throw new Error(`未能在页面上找到目标为 '${target}' 的元素。可用元素: ${JSON.stringify(availableElements.slice(0, 5))}`);
                }

                // 检查命令权限
                if (!securityManager.isCommandAllowed(command, element)) {
                    throw new Error(`安全策略阻止在当前元素上执行 '${command}' 命令`);
                }

                // 高级交互操作处理
                result = await executeAdvancedCommand(command, element, text, request.data);
            }

        } catch (error) {
            result = { status: 'error', error: error.message };
        }

        chrome.runtime.sendMessage({
            type: 'COMMAND_RESULT',
            data: {
                requestId,
                sourceClientId,
                ...result
            }
        });
        setTimeout(sendPageInfoUpdate, 500);
    }
});

// 高级命令执行器
async function executeAdvancedCommand(command, element, text, commandData) {
    switch (command) {
        case 'type':
            return await handleTypeCommand(element, text, commandData);

        case 'click':
            return await handleClickCommand(element, commandData);

        case 'scroll':
            return await handleScrollCommand(element || document.body, commandData);

        case 'drag':
            return await handleDragCommand(element, commandData);

        case 'hover':
            return await handleHoverCommand(element, commandData);

        case 'rightclick':
            return await handleRightClickCommand(element, commandData);

        case 'keypress':
            return await handleKeyPressCommand(element, commandData);

        case 'upload':
            return await handleUploadCommand(element, commandData);

        case 'select':
            return await handleSelectCommand(element, commandData);

        case 'focus':
            return await handleFocusCommand(element, commandData);

        case 'blur':
            return await handleBlurCommand(element, commandData);

        case 'screenshot':
            return await handleScreenshotCommand(element, commandData);

        // 新增的调试和信息获取命令
        case 'debug_page':
            return await handleDebugPageCommand(commandData);

        case 'get_links':
            return await handleGetLinksCommand(commandData);

        case 'find_search_boxes':
            return await handleFindSearchBoxesCommand(commandData);

        case 'capture_page':
            return await handleCapturePageCommand(commandData);

        case 'click_at_coordinates':
            return await handleClickAtCoordinatesCommand(commandData);

        case 'search':
            return await handleSearchCommand(text, commandData);

        case 'select_by_position':
            return await handleSelectByPositionCommand(commandData);

        case 'click_first_video':
            return await handleClickFirstVideoCommand(commandData);

        case 'verify_input':
            return await handleVerifyInputCommand(element, commandData);

        default:
            throw new Error(`不支持的命令: ${command}`);
    }
}

// 处理输入命令
async function handleTypeCommand(element, text, commandData) {
    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        // 清空现有内容
        if (commandData.clear !== false) {
            element.value = '';
        }

        // 模拟真实输入
        element.focus();

        if (commandData.simulate === true) {
            // 逐字符输入模拟
            for (let char of text) {
                element.value += char;
                element.dispatchEvent(new Event('input', { bubbles: true }));
                await new Promise(resolve => setTimeout(resolve, 50)); // 50ms延迟
            }
        } else {
            element.value = text;
        }

        // 触发change事件
        element.dispatchEvent(new Event('change', { bubbles: true }));

        return {
            status: 'success',
            message: `成功在元素中输入文本: "${text}"`
        };
    } else {
        throw new Error(`目标元素不是输入框`);
    }
}

// 处理点击命令
async function handleClickCommand(element, commandData) {
    // 确保元素可见
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await new Promise(resolve => setTimeout(resolve, 100));

    // 模拟真实用户点击
    element.focus();

    const clickOptions = {
        bubbles: true,
        cancelable: true,
        view: window,
        button: commandData.button || 0, // 0=左键, 1=中键, 2=右键
        ctrlKey: commandData.ctrlKey || false,
        shiftKey: commandData.shiftKey || false,
        altKey: commandData.altKey || false
    };

    // 触发完整的点击事件序列
    element.dispatchEvent(new MouseEvent('mousedown', clickOptions));
    await new Promise(resolve => setTimeout(resolve, 10));
    element.dispatchEvent(new MouseEvent('mouseup', clickOptions));
    element.dispatchEvent(new MouseEvent('click', clickOptions));

    return {
        status: 'success',
        message: `成功点击元素`
    };
}

// 处理滚动命令
async function handleScrollCommand(element, commandData) {
    const { direction = 'down', distance = 300, smooth = true } = commandData;

    let scrollOptions = {
        behavior: smooth ? 'smooth' : 'auto'
    };

    if (element === document.body || element === document.documentElement) {
        // 页面滚动
        switch (direction) {
            case 'up':
                window.scrollBy({ top: -distance, ...scrollOptions });
                break;
            case 'down':
                window.scrollBy({ top: distance, ...scrollOptions });
                break;
            case 'left':
                window.scrollBy({ left: -distance, ...scrollOptions });
                break;
            case 'right':
                window.scrollBy({ left: distance, ...scrollOptions });
                break;
            case 'top':
                window.scrollTo({ top: 0, ...scrollOptions });
                break;
            case 'bottom':
                window.scrollTo({ top: document.body.scrollHeight, ...scrollOptions });
                break;
        }
    } else {
        // 元素滚动
        element.scrollIntoView({ behavior: scrollOptions.behavior, block: 'center' });
    }

    return {
        status: 'success',
        message: `成功滚动 ${direction} ${distance}px`
    };
}

// 处理拖拽命令
async function handleDragCommand(element, commandData) {
    const { targetSelector, offsetX = 0, offsetY = 0 } = commandData;

    if (!targetSelector) {
        throw new Error('拖拽命令需要目标选择器');
    }

    const targetElement = document.querySelector(targetSelector) ||
                         document.querySelector(`[vcp-id="${targetSelector}"]`);

    if (!targetElement) {
        throw new Error(`未找到拖拽目标: ${targetSelector}`);
    }

    // 获取元素位置
    const sourceRect = element.getBoundingClientRect();
    const targetRect = targetElement.getBoundingClientRect();

    // 模拟拖拽事件序列
    const dragStartEvent = new DragEvent('dragstart', {
        bubbles: true,
        cancelable: true,
        view: window
    });

    element.dispatchEvent(dragStartEvent);

    // 模拟拖拽到目标位置
    const dragOverEvent = new DragEvent('dragover', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: targetRect.left + offsetX,
        clientY: targetRect.top + offsetY
    });

    targetElement.dispatchEvent(dragOverEvent);

    const dropEvent = new DragEvent('drop', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: targetRect.left + offsetX,
        clientY: targetRect.top + offsetY
    });

    targetElement.dispatchEvent(dropEvent);

    const dragEndEvent = new DragEvent('dragend', {
        bubbles: true,
        cancelable: true,
        view: window
    });

    element.dispatchEvent(dragEndEvent);

    return {
        status: 'success',
        message: `成功拖拽元素到目标位置`
    };
}

// 处理悬停命令
async function handleHoverCommand(element, commandData) {
    const { duration = 1000 } = commandData;

    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const mouseEnterEvent = new MouseEvent('mouseenter', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: centerX,
        clientY: centerY
    });

    const mouseOverEvent = new MouseEvent('mouseover', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: centerX,
        clientY: centerY
    });

    element.dispatchEvent(mouseEnterEvent);
    element.dispatchEvent(mouseOverEvent);

    // 保持悬停状态
    await new Promise(resolve => setTimeout(resolve, duration));

    const mouseLeaveEvent = new MouseEvent('mouseleave', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: centerX,
        clientY: centerY
    });

    element.dispatchEvent(mouseLeaveEvent);

    return {
        status: 'success',
        message: `成功悬停 ${duration}ms`
    };
}

// 处理右键点击命令
async function handleRightClickCommand(element, commandData) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await new Promise(resolve => setTimeout(resolve, 100));

    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const contextMenuEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        cancelable: true,
        view: window,
        button: 2,
        clientX: centerX,
        clientY: centerY
    });

    element.dispatchEvent(contextMenuEvent);

    return {
        status: 'success',
        message: `成功右键点击元素`
    };
}

// 处理按键命令
async function handleKeyPressCommand(element, commandData) {
    const { key, ctrlKey = false, shiftKey = false, altKey = false } = commandData;

    element.focus();

    const keyDownEvent = new KeyboardEvent('keydown', {
        bubbles: true,
        cancelable: true,
        key: key,
        ctrlKey: ctrlKey,
        shiftKey: shiftKey,
        altKey: altKey
    });

    const keyUpEvent = new KeyboardEvent('keyup', {
        bubbles: true,
        cancelable: true,
        key: key,
        ctrlKey: ctrlKey,
        shiftKey: shiftKey,
        altKey: altKey
    });

    element.dispatchEvent(keyDownEvent);
    await new Promise(resolve => setTimeout(resolve, 50));
    element.dispatchEvent(keyUpEvent);

    return {
        status: 'success',
        message: `成功按下按键: ${key}`
    };
}

// 处理文件上传命令
async function handleUploadCommand(element, commandData) {
    if (element.tagName !== 'INPUT' || element.type !== 'file') {
        throw new Error('目标元素不是文件输入框');
    }

    const { files } = commandData;
    if (!files || !Array.isArray(files)) {
        throw new Error('需要提供文件列表');
    }

    // 注意：由于安全限制，实际的文件上传需要用户交互
    // 这里只能模拟触发文件选择对话框
    element.click();

    return {
        status: 'success',
        message: `已触发文件选择对话框`
    };
}

// 处理选择命令（下拉框）
async function handleSelectCommand(element, commandData) {
    if (element.tagName !== 'SELECT') {
        throw new Error('目标元素不是下拉选择框');
    }

    const { value, text, index } = commandData;

    let option = null;

    if (value !== undefined) {
        option = element.querySelector(`option[value="${value}"]`);
    } else if (text !== undefined) {
        option = Array.from(element.options).find(opt => opt.text === text);
    } else if (index !== undefined) {
        option = element.options[index];
    }

    if (!option) {
        throw new Error('未找到匹配的选项');
    }

    option.selected = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));

    return {
        status: 'success',
        message: `成功选择选项: ${option.text}`
    };
}

// 处理聚焦命令
async function handleFocusCommand(element, commandData) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await new Promise(resolve => setTimeout(resolve, 100));
    element.focus();

    return {
        status: 'success',
        message: `成功聚焦元素`
    };
}

// 处理失焦命令
async function handleBlurCommand(element, commandData) {
    element.blur();

    return {
        status: 'success',
        message: `成功使元素失焦`
    };
}

// 处理截图命令
async function handleScreenshotCommand(element, commandData) {
    // 滚动到元素位置
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await new Promise(resolve => setTimeout(resolve, 200));

    // 高亮元素（可选）
    if (commandData.highlight !== false) {
        const originalStyle = element.style.cssText;
        element.style.outline = '3px solid #ff0000';
        element.style.outlineOffset = '2px';

        setTimeout(() => {
            element.style.cssText = originalStyle;
        }, 2000);
    }

    return {
        status: 'success',
        message: `元素已准备截图并高亮显示`,
        elementInfo: {
            tagName: element.tagName,
            className: element.className,
            id: element.id,
            rect: element.getBoundingClientRect()
        }
    };
}

// 处理调试页面命令
async function handleDebugPageCommand(commandData) {
    const allElements = document.querySelectorAll('[vcp-id]');
    const elementInfo = Array.from(allElements).map(el => ({
        vcpId: el.getAttribute('vcp-id'),
        tagName: el.tagName.toLowerCase(),
        type: el.type || '',
        text: (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().substring(0, 100),
        className: el.className,
        id: el.id,
        visible: el.offsetParent !== null,
        rect: {
            x: el.getBoundingClientRect().x,
            y: el.getBoundingClientRect().y,
            width: el.getBoundingClientRect().width,
            height: el.getBoundingClientRect().height
        }
    }));

    return {
        status: 'success',
        message: `页面调试信息获取成功`,
        data: {
            url: window.location.href,
            title: document.title,
            totalElements: elementInfo.length,
            visibleElements: elementInfo.filter(el => el.visible).length,
            elements: elementInfo
        }
    };
}

// 处理获取链接命令
async function handleGetLinksCommand(commandData) {
    const links = Array.from(document.querySelectorAll('a[href]')).map(link => ({
        text: link.innerText.trim().substring(0, 100),
        href: link.href,
        vcpId: link.getAttribute('vcp-id'),
        visible: link.offsetParent !== null
    })).filter(link => link.visible && link.text);

    return {
        status: 'success',
        message: `找到 ${links.length} 个可见链接`,
        data: { links }
    };
}

// 处理查找搜索框命令
async function handleFindSearchBoxesCommand(commandData) {
    const searchBoxes = Array.from(document.querySelectorAll('input')).filter(input => {
        const type = input.type.toLowerCase();
        const placeholder = (input.placeholder || '').toLowerCase();
        const name = (input.name || '').toLowerCase();
        const id = (input.id || '').toLowerCase();

        return type === 'search' ||
               type === 'text' && (
                   placeholder.includes('search') || placeholder.includes('搜索') ||
                   name.includes('search') || name.includes('搜索') ||
                   id.includes('search') || id.includes('搜索')
               );
    }).map(input => ({
        vcpId: input.getAttribute('vcp-id'),
        type: input.type,
        placeholder: input.placeholder,
        name: input.name,
        id: input.id,
        value: input.value,
        visible: input.offsetParent !== null
    }));

    return {
        status: 'success',
        message: `找到 ${searchBoxes.length} 个搜索框`,
        data: { searchBoxes }
    };
}

// 处理页面截图命令
async function handleCapturePageCommand(commandData) {
    // 由于浏览器安全限制，content script 无法直接截图
    // 这里返回页面的基本信息和可交互元素位置
    const elements = Array.from(document.querySelectorAll('[vcp-id]')).map(el => ({
        vcpId: el.getAttribute('vcp-id'),
        text: (el.innerText || el.value || el.placeholder || '').trim().substring(0, 50),
        rect: el.getBoundingClientRect(),
        tagName: el.tagName.toLowerCase()
    }));

    return {
        status: 'success',
        message: `页面信息已准备，包含 ${elements.length} 个可交互元素`,
        data: {
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            scroll: {
                x: window.scrollX,
                y: window.scrollY
            },
            elements: elements
        }
    };
}

// 处理坐标点击命令
async function handleClickAtCoordinatesCommand(commandData) {
    const { x, y } = commandData;
    if (x === undefined || y === undefined) {
        throw new Error('坐标点击需要指定 x 和 y 坐标');
    }

    const element = document.elementFromPoint(x, y);
    if (!element) {
        throw new Error(`坐标 (${x}, ${y}) 处没有找到元素`);
    }

    const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: x,
        clientY: y
    });

    element.dispatchEvent(clickEvent);

    return {
        status: 'success',
        message: `成功点击坐标 (${x}, ${y}) 处的元素: ${element.tagName.toLowerCase()}`
    };
}

// 处理搜索命令
async function handleSearchCommand(text, commandData) {
    if (!text) {
        throw new Error('搜索命令需要指定搜索文本');
    }

    // 查找搜索框
    const searchInputs = Array.from(document.querySelectorAll('input')).filter(input => {
        const type = input.type.toLowerCase();
        const placeholder = (input.placeholder || '').toLowerCase();
        const name = (input.name || '').toLowerCase();
        const id = (input.id || '').toLowerCase();

        return type === 'search' ||
               type === 'text' && (
                   placeholder.includes('search') || placeholder.includes('搜索') ||
                   name.includes('search') || name.includes('搜索') ||
                   id.includes('search') || id.includes('搜索')
               );
    });

    if (searchInputs.length === 0) {
        throw new Error('页面上没有找到搜索框');
    }

    const searchInput = searchInputs[0]; // 使用第一个搜索框
    searchInput.focus();
    searchInput.value = text;
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    searchInput.dispatchEvent(new Event('change', { bubbles: true }));

    // 尝试触发搜索（按回车或点击搜索按钮）
    const enterEvent = new KeyboardEvent('keydown', {
        bubbles: true,
        cancelable: true,
        key: 'Enter',
        keyCode: 13
    });
    searchInput.dispatchEvent(enterEvent);

    return {
        status: 'success',
        message: `成功在搜索框中输入: "${text}"`
    };
}

// 处理按位置选择命令
async function handleSelectByPositionCommand(commandData) {
    const { type, position, action } = commandData;
    if (!type || !position || !action) {
        throw new Error('按位置选择需要指定 type、position 和 action 参数');
    }

    let selector = '';
    switch (type.toLowerCase()) {
        case 'video':
            selector = 'video';
            break;
        case 'image':
            selector = 'img';
            break;
        case 'link':
            selector = 'a[href]';
            break;
        case 'button':
            selector = 'button, input[type="button"], input[type="submit"]';
            break;
        case 'article':
            selector = 'article, .article, [role="article"]';
            break;
        default:
            selector = type;
    }

    const elements = Array.from(document.querySelectorAll(selector)).filter(el => el.offsetParent !== null);

    if (elements.length === 0) {
        throw new Error(`页面上没有找到类型为 "${type}" 的元素`);
    }

    let targetElement;
    if (position === 'first' || position === '1') {
        targetElement = elements[0];
    } else if (position === 'last') {
        targetElement = elements[elements.length - 1];
    } else if (position === 'second' || position === '2') {
        targetElement = elements[1];
    } else {
        const index = parseInt(position) - 1;
        if (index >= 0 && index < elements.length) {
            targetElement = elements[index];
        }
    }

    if (!targetElement) {
        throw new Error(`无法找到第 ${position} 个 "${type}" 元素`);
    }

    if (action === 'click') {
        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await new Promise(resolve => setTimeout(resolve, 100));
        targetElement.click();
        return {
            status: 'success',
            message: `成功点击第 ${position} 个 ${type} 元素`
        };
    } else if (action === 'info') {
        return {
            status: 'success',
            message: `获取第 ${position} 个 ${type} 元素信息`,
            data: {
                tagName: targetElement.tagName.toLowerCase(),
                text: targetElement.innerText?.substring(0, 100),
                src: targetElement.src,
                href: targetElement.href,
                rect: targetElement.getBoundingClientRect()
            }
        };
    }

    throw new Error(`不支持的操作: ${action}`);
}

// 处理点击第一个视频命令
async function handleClickFirstVideoCommand(commandData) {
    const videos = Array.from(document.querySelectorAll('video')).filter(video => video.offsetParent !== null);

    if (videos.length === 0) {
        throw new Error('页面上没有找到视频元素');
    }

    const firstVideo = videos[0];
    firstVideo.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await new Promise(resolve => setTimeout(resolve, 100));
    firstVideo.click();

    return {
        status: 'success',
        message: '成功点击第一个视频'
    };
}

// 处理验证输入命令
async function handleVerifyInputCommand(element, commandData) {
    if (!element) {
        throw new Error('验证输入需要指定目标元素');
    }

    const elementInfo = {
        tagName: element.tagName.toLowerCase(),
        type: element.type || '',
        value: element.value || '',
        placeholder: element.placeholder || '',
        name: element.name || '',
        id: element.id || '',
        disabled: element.disabled,
        readonly: element.readOnly,
        visible: element.offsetParent !== null,
        focused: document.activeElement === element
    };

    return {
        status: 'success',
        message: '输入框验证完成',
        data: elementInfo
    };
}

// 添加设置更新监听器
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'SETTINGS_UPDATED') {
        securityManager.updateSettings(request.settings);
        updateManager.updateThrottle = request.settings.updateInterval || 500;
        console.log('Settings updated:', request.settings);
        sendResponse({ success: true });
    }
});

const debouncedSendPageInfoUpdate = debounce(sendPageInfoUpdate, 500); // 降低延迟，提高响应速度

const observer = new MutationObserver((mutations) => {
    debouncedSendPageInfoUpdate();
});
observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    characterData: true
});

document.addEventListener('click', debouncedSendPageInfoUpdate);
document.addEventListener('focusin', debouncedSendPageInfoUpdate);
document.addEventListener('scroll', debouncedSendPageInfoUpdate, true); // 监听滚动事件

window.addEventListener('load', sendPageInfoUpdate);
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        sendPageInfoUpdate();
    }
});

setInterval(sendPageInfoUpdate, 5000);

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}