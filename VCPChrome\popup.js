document.addEventListener('DOMContentLoaded', () => {
    const statusDiv = document.getElementById('status');
    const toggleButton = document.getElementById('toggleConnection');
    const settingsToggle = document.getElementById('settings-toggle');
    const settingsDiv = document.getElementById('settings');
    const serverUrlInput = document.getElementById('serverUrl');
    const vcpKeyInput = document.getElementById('vcpKey');
    const saveSettingsButton = document.getElementById('saveSettings');

    // 新的安全和性能设置元素
    const enableWhitelistCheckbox = document.getElementById('enableWhitelist');
    const whitelistTextarea = document.getElementById('whitelist');
    const blacklistTextarea = document.getElementById('blacklist');
    const blockSensitiveCheckbox = document.getElementById('blockSensitive');
    const updateIntervalInput = document.getElementById('updateInterval');
    const enableIncrementalUpdateCheckbox = document.getElementById('enableIncrementalUpdate');

    // 更新UI的函数
    function updateUI(isConnected, connectionInfo = null) {
        if (isConnected) {
            statusDiv.innerHTML = `
                <div>已连接到 VCP 服务器</div>
                ${connectionInfo ? `<div class="status-info">
                    连接质量: ${getQualityText(connectionInfo.connectionQuality)} |
                    重连次数: ${connectionInfo.reconnectAttempts}
                </div>` : ''}
            `;
            statusDiv.className = 'connected';
            toggleButton.textContent = '断开连接';
        } else {
            statusDiv.innerHTML = `
                <div>已断开连接</div>
                ${connectionInfo && connectionInfo.reconnectAttempts > 0 ?
                    `<div class="status-info">正在尝试重连... (${connectionInfo.reconnectAttempts}/${connectionInfo.maxReconnectAttempts})</div>` : ''}
            `;
            statusDiv.className = 'disconnected';
            toggleButton.textContent = '连接';
        }
    }

    function getQualityText(quality) {
        const qualityMap = {
            'excellent': '优秀',
            'good': '良好',
            'fair': '一般',
            'poor': '较差',
            'unknown': '未知'
        };
        return qualityMap[quality] || '未知';
    }

    // 加载已保存的设置
    function loadSettings() {
        chrome.storage.local.get([
            'serverUrl', 'vcpKey', 'enableWhitelist', 'whitelist', 'blacklist',
            'blockSensitive', 'updateInterval', 'enableIncrementalUpdate'
        ], (result) => {
            if (result.serverUrl) {
                serverUrlInput.value = result.serverUrl;
            }
            if (result.vcpKey) {
                vcpKeyInput.value = result.vcpKey;
            }

            // 安全设置
            enableWhitelistCheckbox.checked = result.enableWhitelist || false;
            whitelistTextarea.value = result.whitelist || '';
            blacklistTextarea.value = result.blacklist || '';
            blockSensitiveCheckbox.checked = result.blockSensitive || false;

            // 性能设置
            updateIntervalInput.value = result.updateInterval || 500;
            enableIncrementalUpdateCheckbox.checked = result.enableIncrementalUpdate !== false; // 默认启用
        });
    }

    // 页面加载时
    // 1. 加载设置
    loadSettings();
    // 2. 向background script请求当前状态
    chrome.runtime.sendMessage({ type: 'GET_DETAILED_STATUS' }, (response) => {
        if (chrome.runtime.lastError) {
            console.log("Could not establish connection. Background script might be initializing.");
            updateUI(false);
        } else {
            updateUI(response.isConnected, response.connectionInfo);
        }
    });

    // 处理连接/断开按钮点击
    toggleButton.addEventListener('click', () => {
        // 只发送指令，不处理响应
        chrome.runtime.sendMessage({ type: 'TOGGLE_CONNECTION' });
    });

    // 监听来自background script的状态广播
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.type === 'STATUS_UPDATE') {
            updateUI(request.isConnected, request.connectionInfo);
        }
    });

    // 处理设置按钮点击
    settingsToggle.addEventListener('click', () => {
        if (settingsDiv.style.display === 'none') {
            settingsDiv.style.display = 'block';
            settingsToggle.textContent = '隐藏设置';
        } else {
            settingsDiv.style.display = 'none';
            settingsToggle.textContent = '设置';
        }
    });

    // 处理保存设置按钮点击
    saveSettingsButton.addEventListener('click', () => {
        const settings = {
            serverUrl: serverUrlInput.value,
            vcpKey: vcpKeyInput.value,
            enableWhitelist: enableWhitelistCheckbox.checked,
            whitelist: whitelistTextarea.value,
            blacklist: blacklistTextarea.value,
            blockSensitive: blockSensitiveCheckbox.checked,
            updateInterval: parseInt(updateIntervalInput.value) || 500,
            enableIncrementalUpdate: enableIncrementalUpdateCheckbox.checked
        };

        chrome.storage.local.set(settings, () => {
            console.log('Settings saved:', settings);

            // 通知 content script 更新设置
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0]) {
                    chrome.tabs.sendMessage(tabs[0].id, {
                        type: 'SETTINGS_UPDATED',
                        settings: settings
                    }).catch(e => {
                        // 忽略错误，content script 可能未加载
                    });
                }
            });

            // 给用户一个保存成功的提示
            saveSettingsButton.textContent = '已保存!';
            setTimeout(() => {
                saveSettingsButton.textContent = '保存设置';
            }, 1500);
        });
    });

    // 添加强制重连按钮功能
    const forceReconnectButton = document.createElement('button');
    forceReconnectButton.textContent = '强制重连';
    forceReconnectButton.style.marginTop = '10px';
    forceReconnectButton.style.fontSize = '12px';
    forceReconnectButton.addEventListener('click', () => {
        chrome.runtime.sendMessage({ type: 'FORCE_RECONNECT' }, (response) => {
            if (response && response.success) {
                forceReconnectButton.textContent = '重连中...';
                setTimeout(() => {
                    forceReconnectButton.textContent = '强制重连';
                }, 2000);
            }
        });
    });

    // 将强制重连按钮添加到设置区域
    settingsDiv.appendChild(forceReconnectButton);
});