console.log('VCPChrome background.js loaded.');
let ws = null;
let isConnected = false;
const defaultServerUrl = 'ws://localhost:8088'; // 默认服务器地址
const defaultVcpKey = 'your_secret_key'; // 默认密钥

// 高级连接管理
class ConnectionManager {
    constructor() {
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectDelay = 1000; // 初始重连延迟 1秒
        this.maxReconnectDelay = 30000; // 最大重连延迟 30秒
        this.reconnectTimer = null;
        this.heartbeatTimer = null;
        this.heartbeatInterval = 30000; // 心跳间隔 30秒
        this.connectionQuality = 'unknown';
        this.lastPingTime = 0;
        this.networkOnline = navigator.onLine;

        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.networkOnline = true;
            console.log('Network is back online, attempting to reconnect...');
            this.attemptReconnect();
        });

        window.addEventListener('offline', () => {
            this.networkOnline = false;
            console.log('Network went offline');
            this.stopHeartbeat();
        });
    }

    // 计算重连延迟（指数退避）
    getReconnectDelay() {
        const delay = Math.min(
            this.reconnectDelay * Math.pow(2, this.reconnectAttempts),
            this.maxReconnectDelay
        );
        // 添加随机抖动，避免多个客户端同时重连
        return delay + Math.random() * 1000;
    }

    // 开始心跳检测
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                this.lastPingTime = Date.now();
                ws.send(JSON.stringify({
                    type: 'ping',
                    timestamp: this.lastPingTime
                }));
            }
        }, this.heartbeatInterval);
    }

    // 停止心跳检测
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    // 处理心跳响应
    handlePong(timestamp) {
        if (timestamp === this.lastPingTime) {
            const latency = Date.now() - timestamp;
            this.updateConnectionQuality(latency);
        }
    }

    // 更新连接质量
    updateConnectionQuality(latency) {
        if (latency < 100) {
            this.connectionQuality = 'excellent';
        } else if (latency < 300) {
            this.connectionQuality = 'good';
        } else if (latency < 1000) {
            this.connectionQuality = 'fair';
        } else {
            this.connectionQuality = 'poor';
        }

        // 广播连接质量更新
        broadcastStatusUpdate();
    }

    // 尝试重连
    attemptReconnect() {
        if (!this.networkOnline) {
            console.log('Network is offline, skipping reconnect attempt');
            return;
        }

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnect attempts reached, giving up');
            return;
        }

        const delay = this.getReconnectDelay();
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);

        this.reconnectTimer = setTimeout(() => {
            this.reconnectAttempts++;
            connect();
        }, delay);
    }

    // 重置重连计数器
    resetReconnectAttempts() {
        this.reconnectAttempts = 0;
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }

    // 获取连接状态信息
    getConnectionInfo() {
        return {
            isConnected,
            reconnectAttempts: this.reconnectAttempts,
            maxReconnectAttempts: this.maxReconnectAttempts,
            connectionQuality: this.connectionQuality,
            networkOnline: this.networkOnline
        };
    }
}

const connectionManager = new ConnectionManager();

function connect() {
    if (ws && ws.readyState === WebSocket.OPEN) {
        console.log('WebSocket is already connected.');
        return;
    }

    // 从storage获取URL和Key
    chrome.storage.local.get(['serverUrl', 'vcpKey'], (result) => {
        const serverUrlToUse = result.serverUrl || defaultServerUrl;
        const keyToUse = result.vcpKey || defaultVcpKey;

        const fullUrl = `${serverUrlToUse}/vcp-chrome-observer/VCP_Key=${keyToUse}`;
        console.log('Connecting to:', fullUrl);

        ws = new WebSocket(fullUrl);

        ws.onopen = () => {
            console.log('WebSocket connection established.');
            isConnected = true;
            connectionManager.resetReconnectAttempts();
            connectionManager.startHeartbeat();
            updateIcon();
            broadcastStatusUpdate(); // 广播最新状态
        };

        ws.onmessage = (event) => {
            console.log('Message from server:', event.data);
            try {
                const message = JSON.parse(event.data);

                // 处理心跳响应
                if (message.type === 'pong') {
                    connectionManager.handlePong(message.timestamp);
                    return;
                }

                // 处理来自服务器的指令
                if (message.type === 'command') {
                    const commandData = message.data;
                    console.log('Received commandData:', commandData);

                    // 检查是否是 open_url 指令
                    if (commandData.command === 'open_url' && commandData.url) {
                        console.log('Handling open_url command. URL:', commandData.url);
                        let fullUrl = commandData.url;
                        if (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://')) {
                            fullUrl = 'https://' + fullUrl;
                        }
                        console.log('Attempting to create tab with URL:', fullUrl);
                        chrome.tabs.create({ url: fullUrl }, (tab) => {
                            if (chrome.runtime.lastError) {
                                const errorMessage = `创建标签页失败: ${chrome.runtime.lastError.message}`;
                                console.error('Error creating tab:', errorMessage);
                                if (ws && ws.readyState === WebSocket.OPEN) {
                                ws.send(JSON.stringify({
                                    type: 'command_result',
                                    data: {
                                        requestId: commandData.requestId,
                                        status: 'error',
                                        error: errorMessage
                                    }
                                }));
                            }
                        } else {
                            console.log('Tab created successfully. Tab ID:', tab.id, 'URL:', tab.url);
                            if (ws && ws.readyState === WebSocket.OPEN) {
                                ws.send(JSON.stringify({
                                    type: 'command_result',
                                    data: {
                                        requestId: commandData.requestId,
                                        sourceClientId: commandData.sourceClientId, // 确保返回 sourceClientId
                                        status: 'success',
                                        message: `成功打开URL: ${commandData.url}`
                                    }
                                }));
                            }
                        }
                    });
                } else {
                    console.log('Forwarding command to content script:', commandData);
                    forwardCommandToContentScript(commandData);
                }
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        ws.onclose = (event) => {
            console.log('WebSocket connection closed.', {
                code: event.code,
                reason: event.reason,
                wasClean: event.wasClean
            });
            isConnected = false;
            connectionManager.stopHeartbeat();
            ws = null;
            updateIcon();
            broadcastStatusUpdate();

            // 根据关闭原因决定是否重连
            if (!event.wasClean && event.code !== 1000) {
                console.log('Connection closed unexpectedly, attempting to reconnect...');
                connectionManager.attemptReconnect();
            }
        };

        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            isConnected = false;
            connectionManager.stopHeartbeat();
            ws = null;
            updateIcon();
            broadcastStatusUpdate();

            // 网络错误时尝试重连
            setTimeout(() => {
                connectionManager.attemptReconnect();
            }, 1000);
        };
    });
}

function disconnect() {
    connectionManager.resetReconnectAttempts(); // 停止自动重连
    connectionManager.stopHeartbeat();
    if (ws) {
        ws.close(1000, 'User requested disconnect'); // 正常关闭
    }
}

function updateIcon() {
    const iconPath = isConnected ? 'icons/icon48.png' : 'icons/icon_disconnected.png'; // 你需要创建一个断开连接的图标
    // 为了简单起见，我们先只改变徽章
    chrome.action.setBadgeText({ text: isConnected ? 'On' : 'Off' });
    chrome.action.setBadgeBackgroundColor({ color: isConnected ? '#00C853' : '#FF5252' });
}

// 监听来自popup和content_script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'GET_STATUS') {
        const connectionInfo = connectionManager.getConnectionInfo();
        sendResponse({
            isConnected: isConnected,
            connectionInfo: connectionInfo
        });
    } else if (request.type === 'GET_DETAILED_STATUS') {
        // 提供详细的连接状态信息
        const connectionInfo = connectionManager.getConnectionInfo();
        sendResponse({
            isConnected: isConnected,
            connectionInfo: connectionInfo,
            wsReadyState: ws ? ws.readyState : WebSocket.CLOSED,
            timestamp: Date.now()
        });
    } else if (request.type === 'FORCE_RECONNECT') {
        // 强制重连
        if (ws) {
            ws.close();
        }
        connectionManager.resetReconnectAttempts();
        setTimeout(() => connect(), 100);
        sendResponse({ success: true });
    } else if (request.type === 'TOGGLE_CONNECTION') {
        if (isConnected) {
            disconnect();
        } else {
            connect();
        }
        // 不再立即返回状态，而是等待广播
        // sendResponse({ isConnected: !isConnected });
    } else if (request.type === 'PAGE_INFO_UPDATE') {
        // 从content_script接收到页面信息，发送到服务器
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
                type: 'pageInfoUpdate',
                data: { markdown: request.data.markdown }
            }));
        }
    } else if (request.type === 'COMMAND_RESULT') {
        // 从content_script接收到命令执行结果，发送到服务器
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
                type: 'command_result',
                data: request.data
            }));
        }
    }
    return true; // 保持消息通道开放以进行异步响应
});

function forwardCommandToContentScript(commandData) {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
                type: 'EXECUTE_COMMAND',
                data: commandData
            });
        }
    });
}

function broadcastStatusUpdate() {
    const connectionInfo = connectionManager.getConnectionInfo();
    chrome.runtime.sendMessage({
        type: 'STATUS_UPDATE',
        isConnected: isConnected,
        connectionInfo: connectionInfo
    }).catch(error => {
        // 捕获当popup未打开时发送消息产生的错误，这是正常现象
        if (error.message.includes("Could not establish connection. Receiving end does not exist.")) {
            // This is expected if the popup is not open.
        } else {
            console.error("Error broadcasting status:", error);
        }
    });
}

// 监听标签页切换
chrome.tabs.onActivated.addListener((activeInfo) => {
    // 请求新激活的标签页更新信息
    chrome.tabs.sendMessage(activeInfo.tabId, { type: 'REQUEST_PAGE_INFO_UPDATE' }).catch(e => {
        if (!e.message.includes("Could not establish connection")) console.log("Error sending to content script on tab activation:", e.message);
    });
});

// 监听标签页URL变化或加载状态变化
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当导航开始时，清除内容脚本的状态以防止内容累积
    if (changeInfo.status === 'loading') {
        chrome.tabs.sendMessage(tabId, { type: 'CLEAR_STATE' }).catch(e => {
            // This error is expected if the content script hasn't been injected yet
            if (!e.message.includes("Could not establish connection")) console.log("Error sending CLEAR_STATE:", e.message);
        });
    }
    // 当页面加载完成时，或者URL变化后加载完成时，请求更新
    if (changeInfo.status === 'complete' && tab.active) {
        chrome.tabs.sendMessage(tabId, { type: 'REQUEST_PAGE_INFO_UPDATE' }).catch(e => {
            if (!e.message.includes("Could not establish connection")) console.log("Error sending to content script on tab update:", e.message);
        });
    }
});

// 初始化图标状态
updateIcon();